﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitecture.API.Requests.Status;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.StatusRequests;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface IStatusService 
	{
		Task<Result<StatusDto>> GetByIdAsync(int id);
		Task<Result<IEnumerable<StatusDto>>> GetAllByProjectKeyAsync(string projectKey);
		Task<Result<StatusDto>> CreateAsync(CreateStatusRequest request);
		Task<Result<bool>> UpdateAsync(int id, UpdateStatusRequest request);
		Task<Result<bool>> SoftDeleteAsync(int id);
		Task<Result<bool>> ReorderAsync(ReorderStatusRequest request);
	}
}
