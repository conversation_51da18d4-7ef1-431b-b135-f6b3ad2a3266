using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class PriorityRepository : Repository<Priority>, IPriorityRepository
	{
		public PriorityRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<bool> IsNameDuplicated(string projectKey, string name)
		{
			return await _context.Priorities.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted);
		}

		public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
		{
			return await _context.Priorities
				.Where(c => c.ProjectKey == projectKey && !c.IsDeleted)
				.Select(c => (int?)c.Order)
				.MaxAsync() ?? 0;
		}

		public async Task<IEnumerable<Priority>> GetAllByProjectKeyAsync(string projectKey)
		{
			var priorities = await _context.Priorities.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).OrderBy(c => c.Order).ToListAsync();
			return priorities;
		}

		public async Task ReorderPrioritiesAsync(string projectKey, List<int> priorityIdsInOrder)
		{
			var priorities = await _context.Priorities
				.Where(s => s.ProjectKey == projectKey && !s.IsDeleted && priorityIdsInOrder.Contains(s.Id))
				.ToListAsync();

			for (int i = 0; i < priorityIdsInOrder.Count; i++)
			{
				var priority = priorities.FirstOrDefault(s => s.Id == priorityIdsInOrder[i]);
				if (priority != null)
				{
					priority.Order = i + 1;
					_context.Priorities.Update(priority);
				}
			}

			await _context.SaveChangesAsync();
		}
	}
}
