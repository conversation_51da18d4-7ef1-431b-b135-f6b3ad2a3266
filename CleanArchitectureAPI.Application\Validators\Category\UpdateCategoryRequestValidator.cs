﻿using FluentValidation;
using CleanArchitectureAPI.Application;

namespace CleanArchitectureAPI.Application.Validators.Category
{
	public class UpdateCategoryRequestValidator : AbstractValidator<UpdateCategoryRequest>
	{
		public UpdateCategoryRequestValidator()
		{
			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("Tên category không được trống")
				.MinimumLength(2).WithMessage("Tên category không nhỏ hơn 2 ký tự")
				.MaximumLength(100).WithMessage("Tên category tối đa 100 ký tự");

			RuleFor(x => x.Description)
				.MaximumLength(500).WithMessage("Mô tả tối đa 500 ký tự");

			RuleFor(x => x.Color)
				.NotEmpty().WithMessage("Màu sắc không được để trống")
				.Matches("^#(?:[0-9a-fA-F]{3}){1,2}$").WithMessage("Màu sắc nên là mã HEX (Ví dụ: #FF0000).");
		}
	}
}
