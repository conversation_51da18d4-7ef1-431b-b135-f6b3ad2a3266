using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitecture.API.Requests.Status;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.StatusRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace CleanArchitectureAPI.Application.Services
{
    public class StatusService : IStatusService
    {
        private readonly IStatusRepository _statusRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IProjectAccessService _projectAccessService;

        public StatusService(IStatusRepository statusRepository, ICurrentUserService currentUserService, IProjectAccessService projectAccessService)
        {
            _statusRepository = statusRepository;
            _currentUserService = currentUserService;
            _projectAccessService = projectAccessService;
        }

        public async Task<Result<StatusDto>> CreateAsync(CreateStatusRequest request)
        {
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<StatusDto>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Check if name is duplicated
            var isNameDuplicated = await _statusRepository.IsNameDuplicated(request.ProjectKey, request.Name);
            if (isNameDuplicated)
            {
                return Result<StatusDto>.Failure(ErrorCode.Conflict, "Tên trạng thái đã tồn tại trong dự án này.");
            }

            var status = new Status
            {
                Name = request.Name,
                Description = request.Description,
                Color = request.Color,
                ProjectKey = request.ProjectKey,
                Order = await _statusRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
                ProjectId = projectId.Value, // TODO: Lấy ProjectId từ ProjectKey
                CreatedById = _currentUserService.GetUserId(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
            };

            await _statusRepository.CreateAsync(status);

            return Result<StatusDto>.Success(status.Adapt<StatusDto>());

        }

        public async Task<Result<IEnumerable<StatusDto>>> GetAllByProjectKeyAsync(string projectKey)
        {
            IEnumerable<Status> statuses = await _statusRepository.GetAllByProjectKeyAsync(projectKey);
            var statusDtos = statuses.Adapt<IEnumerable<StatusDto>>();
            return Result<IEnumerable<StatusDto>>.Success(statusDtos);
        }

        public async Task<Result<StatusDto>> GetByIdAsync(int id)
        {
            var status = await _statusRepository.GetByIdAsync(id);
            if (status == null)
                return Result<StatusDto>.Failure(ErrorCode.NotFound, "Không tìm thấy trạng thái");

            return Result<StatusDto>.Success(status.Adapt<StatusDto>());
        }

        public async Task<Result<bool>> ReorderAsync(ReorderStatusRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.StatusIdsInOrder == null || !request.StatusIdsInOrder.Any())
            {
                return Result<bool>.Failure("Dữ liệu không hợp lệ.");
            }

            await _statusRepository.ReorderStatusesAsync(request.ProjectKey, request.StatusIdsInOrder);
            return Result<bool>.Success(true);
        }
      
        public async Task<Result<bool>> SoftDeleteAsync(int id)
        {
            var status = await _statusRepository.GetByIdAsync(id);
            if (status == null)
            {
                return Result<bool>.Failure("Không tìm thấy trạng thái.");
            }
            status.DeletedById = _currentUserService.GetUserId();
            status.DeletedAt = DateTime.UtcNow;
            await _statusRepository.SoftDeleteAsync(id);

            return Result<bool>.Success(true);
        }
        public async Task<Result<bool>> UpdateAsync(int id, UpdateStatusRequest request)
        {
            var status = await _statusRepository.GetByIdAsync(id);
            if (status == null)
            {
                return Result<bool>.Failure("Không tìm thấy trạng thái.");
            }

            var existingStatus = await _statusRepository.GetAllByProjectKeyAsync(request.ProjectKey);
            var isNameDuplicated = existingStatus
                .Where(s => s.Id != id && s.ProjectKey == status.ProjectKey && s.Name == request.Name)
                .Any();

            if (isNameDuplicated)
            {
                return Result<bool>.Failure("Tên trạng thái đã tồn tại trong dự án này.");
            }
            status.Name = request.Name;
            status.Description = request.Description;
            status.Color = request.Color;
            status.LastModifiedAt = DateTime.UtcNow;
            status.LastModifiedById = _currentUserService.GetUserId();
            status.IsActive = request.IsActive;

            await _statusRepository.UpdateAsync(status);

            return Result<bool>.Success(true);
        }
    }
}
