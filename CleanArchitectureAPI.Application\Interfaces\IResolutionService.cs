﻿using CleanArchitecture.API.Requests.Resolution;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IResolutionService
    {
        Task<IEnumerable<Resolution>> GetAllByProjectKeyAsync(string projectKey);
        Task<Resolution?> GetByIdAsync(int id);
        Task<Result<Resolution>> CreateAsync(CreateResolutionRequest request);
        Task<bool> UpdateAsync(int id, UpdateResolutionRequest request);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> ReorderAsync(List<int> ids);
    }
}