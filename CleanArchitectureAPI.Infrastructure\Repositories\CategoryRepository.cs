﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class CategoryRepository : Repository<Category>, ICategoryRepository
	{
		public CategoryRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<bool> IsNameDuplicated(string projectKey, string name)
		{
			return await _context.Categories.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted);
		}

		public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
		{
			return await _context.Categories
				.Where(c => c.ProjectKey == projectKey && !c.IsDeleted)
				.Select(c => (int?)c.Order)
				.MaxAsync() ?? 0;
		}

		public async Task<IEnumerable<Category>> GetAllByProjectKeyAsync(string projectKey)
		{
			var categories = await _context.Categories.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).OrderBy(c => c.Order).ToListAsync();
			return categories;
		}

		public async Task ReorderCategoriesAsync(string projectKey, List<int> categoryIdsInOrder)
		{
			var categories = await _context.Categories
				.Where(s => s.ProjectKey == projectKey && !s.IsDeleted && categoryIdsInOrder.Contains(s.Id))
				.ToListAsync();

			for (int i = 0; i < categoryIdsInOrder.Count; i++)
			{
				var category = categories.FirstOrDefault(s => s.Id == categoryIdsInOrder[i]);
				if (category != null)
				{
					category.Order = i + 1;
					_context.Categories.Update(category);
				}
			}

			await _context.SaveChangesAsync();
		}
	}
}
