﻿using CleanArchitecture.API.Requests.Version;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;
using Version = CleanArchitectureAPI.Domain.Entities.Version;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IVersionService
    {
        Task<IEnumerable<Version>> GetAllByProjectKeyAsync(string projectKey);
        Task<Version?> GetByIdAsync(int id);
        Task<Result<Version>> CreateAsync(CreateVersionRequest request);
        Task<bool> UpdateAsync(int id, UpdateVersionRequest request);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> ReorderAsync(List<int> ids);
    }
}