using CleanArchitecture.API.Requests.Version;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.VersionRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IVersionService
    {
        Task<Result<IEnumerable<VersionDto>>> GetAllByProjectKeyAsync(string projectKey);
        Task<Result<VersionDto>> GetByIdAsync(int id);
        Task<Result<VersionDto>> CreateAsync(CreateVersionRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateVersionRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderVersionRequest request);
    }
}