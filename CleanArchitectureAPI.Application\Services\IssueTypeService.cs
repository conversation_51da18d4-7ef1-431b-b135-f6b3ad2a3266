﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Application.Interfaces;

namespace CleanArchitectureAPI.Application.Services
{
    public class IssueTypeService : IIssueTypeService
    {
        private readonly IIssueTypeRepository _issueTypeRepository;
│           private readonly ICurrentUserService _currentUserService;
│           private readonly IProjectAccessService _projectAccessService;
│
│           public IssueTypeService(IIssueTypeRepository issueTypeRepository, ICurrentUserService currentUserService, IProjectAccessService projectAccessService)
│           {
│               _issueTypeRepository = issueTypeRepository;
│               _currentUserService = currentUserService;
│               _projectAccessService = projectAccessService;
│           }
}
}
