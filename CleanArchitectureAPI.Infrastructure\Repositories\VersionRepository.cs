using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class VersionRepository : Repository<Version>, IVersionRepository
	{
		public VersionRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<bool> IsNameDuplicated(string projectKey, string name)
		{
			return await _context.Versions.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted);
		}

		public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
		{
			return await _context.Versions
				.Where(c => c.ProjectKey == projectKey && !c.IsDeleted)
				.Select(c => (int?)c.Order)
				.MaxAsync() ?? 0;
		}

		public async Task<IEnumerable<Version>> GetAllByProjectKeyAsync(string projectKey)
		{
			var versions = await _context.Versions.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).OrderBy(c => c.Order).ToListAsync();
			return versions;
		}

		public async Task ReorderVersionsAsync(string projectKey, List<int> versionIdsInOrder)
		{
			var versions = await _context.Versions
				.Where(s => s.ProjectKey == projectKey && !s.IsDeleted && versionIdsInOrder.Contains(s.Id))
				.ToListAsync();

			for (int i = 0; i < versionIdsInOrder.Count; i++)
			{
				var version = versions.FirstOrDefault(s => s.Id == versionIdsInOrder[i]);
				if (version != null)
				{
					version.Order = i + 1;
					_context.Versions.Update(version);
				}
			}

			await _context.SaveChangesAsync();
		}
	}
}
