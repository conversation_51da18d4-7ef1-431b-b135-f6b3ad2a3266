﻿using CleanArchitecture.API.Requests.Milestone;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IMilestoneService
    {
        Task<IEnumerable<Milestone>> GetAllByProjectKeyAsync(string projectKey);
        Task<Milestone?> GetByIdAsync(int id);
        Task<Result<Milestone>> CreateAsync(CreateMilestoneRequest request);
        Task<bool> UpdateAsync(int id, UpdateMilestoneRequest request);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> ReorderAsync(List<int> ids);
    }
}