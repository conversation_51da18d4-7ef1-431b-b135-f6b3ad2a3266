using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface IVersionRepository : IRepository<Version>
	{
		Task<bool> IsNameDuplicated(string projectKey, string name);
		Task<int> GetMaxOrderInProjectAsync(string projectKey);
		Task<IEnumerable<Version>> GetAllByProjectKeyAsync(string projectKey);
		Task ReorderVersionsAsync(string projectKey, List<int> versionIdsInOrder);
	}
}
