﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface IStatusRepository : IRepository<Status>
	{
		Task<bool> IsNameDuplicated(string projectKey, string name);
		Task<int> GetMaxOrderInProjectAsync(string projectKey);
		Task<IEnumerable<Status>> GetAllByProjectKeyAsync(string projectKey);
		Task ReorderStatusesAsync(string projectKey, List<int> statusIdsInOrder);
    }
}
