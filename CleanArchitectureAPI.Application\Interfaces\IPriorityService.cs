﻿using CleanArchitecture.API.Requests.Priority;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.PriorityRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IPriorityService
    {
        Task<Result<IEnumerable<PriorityDto>>> GetAllByProjectKeyAsync(string projectKey);
        Task<Result<PriorityDto>> GetByIdAsync(int id);
        Task<Result<PriorityDto>> CreateAsync(CreatePriorityRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdatePriorityRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderPriorityRequest request);
    }
}