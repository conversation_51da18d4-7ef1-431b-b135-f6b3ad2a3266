﻿using CleanArchitecture.API.Requests.Priority;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IPriorityService
    {
        Task<IEnumerable<Priority>> GetAllByProjectKeyAsync(string projectKey);
        Task<Priority?> GetByIdAsync(int id);
        Task<Result<Priority>> CreateAsync(CreatePriorityRequest request);
        Task<bool> UpdateAsync(int id, UpdatePriorityRequest request);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> ReorderAsync(List<int> ids);
    }
}