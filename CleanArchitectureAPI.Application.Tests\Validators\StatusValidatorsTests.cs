using System;
using System.Linq;
using System.Reflection;
using FluentValidation;
using Xunit;

namespace CleanArchitectureAPI.Application.Tests.Validators
{
    public class StatusValidatorsTests
    {
        [Theory]
        [InlineData("CleanArchitectureAPI.Application.Validators.Status.CreateStatusRequestValidator")]
        [InlineData("CleanArchitectureAPI.Application.Validators.Status.UpdateStatusRequestValidator")]
        public void StatusValidators_ShouldExist(string validatorFullName)
        {
            var assembly = typeof(CleanArchitectureAPI.Application.Validators.CreateProjectRequestValidator).Assembly;
            var type = assembly.GetType(validatorFullName, throwOnError: false);
            Assert.NotNull(type);
        }

        [Fact]
        public void CreateProjectRequestValidator_IsAbstractValidator()
        {
            var type = typeof(CleanArchitectureAPI.Application.Validators.CreateProjectRequestValidator);
            Assert.True(typeof(IValidator).IsAssignableFrom(type));
        }
    }
}
