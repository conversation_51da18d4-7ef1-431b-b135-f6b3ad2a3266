using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Requests.StatusRequests
{
    public class ReorderStatusRequest
    {
        public string ProjectKey { get; set; } = string.Empty;
        public List<int> StatusIdsInOrder { get; set; } = new List<int>();
    }
}
