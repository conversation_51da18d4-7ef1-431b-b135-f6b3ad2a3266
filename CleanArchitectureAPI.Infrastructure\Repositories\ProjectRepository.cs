﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class ProjectRepository : Repository<Project>, IProjectRepository
	{
		public ProjectRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<int?> GetByKeyAsync(string projectKey)
		{
			var project = await _context.Projects
				.FirstOrDefaultAsync(p => p.ProjectKey == projectKey);

			if (project == null)
				return null;

			return project.Id;
		}


		public async Task<bool> IsNameDuplicated(string name)
		{
			return await _context.Projects.AnyAsync(project => project.Name == name);
		}

		public async Task<bool> IsProjectKeyDuplicatedAsync(string projectKey)
		{
			return await _context.Projects.AnyAsync(project => project.ProjectKey == projectKey);
		}
	}
}
