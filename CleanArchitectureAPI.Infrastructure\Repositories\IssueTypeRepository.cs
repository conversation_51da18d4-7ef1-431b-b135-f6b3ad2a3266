﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class IssueTypeRepository : Repository<IssueType>, IIssueTypeRepository
    {
        public IssueTypeRepository(ApplicationDbContext context) : base(context)
        {
        }

        public Task<IEnumerable<IssueType>> GetAllByProjectKeyAsync(string projectKey)
        {
            throw new NotImplementedException();
        }

        public Task<int> GetMaxOrderInProjectAsync(string projectKey)
        {
            throw new NotImplementedException();
        }

        public Task<bool> IsNameDuplicated(string projectKey, string name)
        {
            throw new NotImplementedException();
        }

        public Task ReorderStatusesAsync(string projectKey, List<int> statusIdsInOrder)
        {
            throw new NotImplementedException();
        }
    }
}
