﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.CategoryRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
	public class CategoryServices : ICategoryService
	{
		private readonly ICategoryRepository _categoryRepository;
		private readonly ICurrentUserService _currentUserService;
		private readonly IProjectAccessService _projectAccessService;

		public CategoryServices(ICategoryRepository categoryRepository, ICurrentUserService currentUserService, IProjectAccessService projectAccessService)
		{
			_categoryRepository = categoryRepository;
			_currentUserService = currentUserService;
			_projectAccessService = projectAccessService;
		}

		public async Task<Result<CategoryDto>> CreateAsync(CreateCategoryRequest request)
		{
			int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
			if (projectId is null)
				return Result<CategoryDto>.Failure(ErrorCode.NotFound, "Project không tồn tại");

			// Check if name is duplicated
			var isNameDuplicated = await _categoryRepository.IsNameDuplicated(request.ProjectKey, request.Name);
			if (isNameDuplicated)
			{
				return Result<CategoryDto>.Failure(ErrorCode.Conflict, "Tên danh mục đã tồn tại trong dự án này.");
			}

			var category = new Category
			{
				Name = request.Name,
				Description = request.Description,
				Color = request.Color,
				ProjectKey = request.ProjectKey,
				Order = await _categoryRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
				ProjectId = projectId.Value,
				CreatedById = _currentUserService.GetUserId(),
				IsActive = true,
				CreatedAt = DateTime.UtcNow,
			};

			await _categoryRepository.CreateAsync(category);

			return Result<CategoryDto>.Success(category.Adapt<CategoryDto>());
		}

		public async Task<Result<CategoryDto>> GetByIdAsync(int id)
		{
			var category = await _categoryRepository.GetByIdAsync(id);
			if (category == null)
				return Result<CategoryDto>.Failure(ErrorCode.NotFound, "Không tìm thấy danh mục");

			return Result<CategoryDto>.Success(category.Adapt<CategoryDto>());
		}

		public async Task<Result<IEnumerable<CategoryDto>>> GetAllByProjectKeyAsync(string projectKey)
		{
			IEnumerable<Category> categories = await _categoryRepository.GetAllByProjectKeyAsync(projectKey);
			var categoryDtos = categories.Adapt<IEnumerable<CategoryDto>>();
			return Result<IEnumerable<CategoryDto>>.Success(categoryDtos);
		}

		public async Task<Result<bool>> ReorderAsync(ReorderCategoryRequest request)
		{
			if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.CategoryIdsInOrder == null || !request.CategoryIdsInOrder.Any())
			{
				return Result<bool>.Failure("Dữ liệu không hợp lệ.");
			}

			await _categoryRepository.ReorderCategoriesAsync(request.ProjectKey, request.CategoryIdsInOrder);
			return Result<bool>.Success(true);
		}

		public async Task<Result<bool>> SoftDeleteAsync(int id)
		{
			var category = await _categoryRepository.GetByIdAsync(id);
			if (category == null)
			{
				return Result<bool>.Failure("Không tìm thấy danh mục.");
			}
			category.DeletedById = _currentUserService.GetUserId();
			category.DeletedAt = DateTime.UtcNow;
			await _categoryRepository.SoftDeleteAsync(id);

			return Result<bool>.Success(true);
		}

		public async Task<Result<bool>> UpdateAsync(int id, UpdateCategoryRequest request)
		{
			var category = await _categoryRepository.GetByIdAsync(id);
			if (category == null)
			{
				return Result<bool>.Failure("Không tìm thấy danh mục.");
			}

			var existingCategories = await _categoryRepository.GetAllByProjectKeyAsync(request.ProjectKey);
			var isNameDuplicated = existingCategories
				.Where(s => s.Id != id && s.ProjectKey == category.ProjectKey && s.Name == request.Name)
				.Any();

			if (isNameDuplicated)
			{
				return Result<bool>.Failure("Tên danh mục đã tồn tại trong dự án này.");
			}
			category.Name = request.Name;
			category.Description = request.Description;
			category.Color = request.Color;
			category.LastModifiedAt = DateTime.UtcNow;
			category.LastModifiedById = _currentUserService.GetUserId();
			category.IsActive = request.IsActive;

			await _categoryRepository.UpdateAsync(category);

			return Result<bool>.Success(true);
		}
	}
}
