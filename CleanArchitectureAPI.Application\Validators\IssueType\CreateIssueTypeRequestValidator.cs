﻿using FluentValidation;
using CleanArchitecture.API.Requests;

namespace CleanArchitectureAPI.Application.Validators.IssueType
{
	public class CreateIssueTypeRequestValidator : AbstractValidator<CreateIssueTypeRequest>
	{
		public CreateIssueTypeRequestValidator()
		{
			RuleFor(x => x.<PERSON>ey)
				.NotEmpty().WithMessage("Vui lòng nhập mã dự án.")
				.MaximumLength(50).WithMessage("Mã dự án không được vượt quá 50 ký tự.");

			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("Vui lòng nhập tên kiểu issue.")
				.MaximumLength(100).WithMessage("Tên kiểu issue không được vượt quá 100 ký tự.");

			RuleFor(x => x.Description)
				.MaximumLength(500).WithMessage("Mô tả tối đa 500 ký tự");
		}
	}
}
