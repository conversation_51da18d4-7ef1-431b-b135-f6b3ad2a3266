﻿using CleanArchitectureAPI.Application.DTOs;

namespace CleanArchitecture.API.Requests.IssueType
{
	public class UpdateIssueTypeRequest : BaseRequest
	{
        public class UpdateIssueTypeRequest
        {
            public string Name { get; set; } = default!;
            public string? Color { get; set; }
            public string? Description { get; set; }
            public bool? IsDefault { get; set; }
        }
    }
}
