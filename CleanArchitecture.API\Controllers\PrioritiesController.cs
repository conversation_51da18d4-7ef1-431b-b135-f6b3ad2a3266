﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Priority;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PrioritiesController : ControllerBase
    {
        private readonly IPriorityService _priorityService;

        public PrioritiesController(IPriorityService priorityService)
        {
            _priorityService = priorityService;
        }

        [HttpGet("{projectKey}/priorities")]
        public async Task<ActionResult<IEnumerable<Priority>>> GetAllPrioritiesByProjectKey([FromRoute] string projectKey)
        {
            var priorities = await _priorityService.GetAllByProjectKeyAsync(projectKey);
            return Ok(priorities);
        }

        [HttpGet("priorities/{id}")]
        public async Task<ActionResult<Priority>> GetPriorityById(int id)
        {
            var priority = await _priorityService.GetByIdAsync(id);
            return Ok(priority);
        }

        [HttpPost]
        public async Task<IActionResult> CreatePriority([FromBody] CreatePriorityRequest request)
        {
            var result = await _priorityService.CreateAsync(request);

            if (!result.IsSuccess)
                return result.ToActionResult();

            return CreatedAtAction(nameof(GetPriorityById), new { id = result.Data!.Id }, request);
        }

        [HttpPut("priorities/{id}")]
        public async Task<IActionResult> UpdatePriority(int id, [FromBody] UpdatePriorityRequest request)
        {
            var updated = await _priorityService.UpdateAsync(id, request);
            if (!updated)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }

        [HttpDelete("priorities/{id}")]
        public async Task<IActionResult> SoftDeletePriority(int id)
        {
            var deleted = await _priorityService.SoftDeleteAsync(id);
            if (!deleted)
                throw new Exception("Ko cap nhat duoc");

            return NoContent();
        }

        [HttpPatch("priorities/reoder")]
        public async Task<IActionResult> ReoderPriority([FromBody] List<int> ids)
        {
            var reordered = await _priorityService.ReorderAsync(ids);
            if (!reordered)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }
    }
}