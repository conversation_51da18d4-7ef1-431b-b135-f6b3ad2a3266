﻿using FluentValidation;
using CleanArchitecture.API.Requests.Resolution;

namespace CleanArchitectureAPI.Application.Validators.Resolution
{
	public class UpdateResolutionRequestValidator : AbstractValidator<UpdateResolutionRequest>
	{
		public UpdateResolutionRequestValidator()
		{
			RuleFor(x => x.ProjectKey)
				.NotEmpty().WithMessage("Vui lòng nhập mã dự án.")
				.MaximumLength(50).WithMessage("Mã dự án không được vượt quá 50 ký tự.");

			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("<PERSON>ui lòng nhập tên resolution.")
				.MaximumLength(100).WithMessage("Tên resolution không được vượt quá 100 ký tự.");

			RuleFor(x => x.Description)
				.MaximumLength(500).WithMessage("Mô tả tối đa 500 ký tự");
		}
	}
}
