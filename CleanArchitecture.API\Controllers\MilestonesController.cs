﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Milestone;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class MilestonesController : ControllerBase
    {
        private readonly IMilestoneService _milestoneService;

        public MilestonesController(IMilestoneService milestoneService)
        {
            _milestoneService = milestoneService;
        }

        [HttpGet("{projectKey}/milestones")]
        public async Task<ActionResult<IEnumerable<Milestone>>> GetAllMilestonesByProjectKey([FromRoute] string projectKey)
        {
            var milestones = await _milestoneService.GetAllByProjectKeyAsync(projectKey);
            return Ok(milestones);
        }

        [HttpGet("milestones/{id}")]
        public async Task<ActionResult<Milestone>> GetMilestoneById(int id)
        {
            var milestone = await _milestoneService.GetByIdAsync(id);
            return Ok(milestone);
        }

        [HttpPost]
        public async Task<IActionResult> CreateMilestone([FromBody] CreateMilestoneRequest request)
        {
            var result = await _milestoneService.CreateAsync(request);

            if (!result.IsSuccess)
                return result.ToActionResult();

            return CreatedAtAction(nameof(GetMilestoneById), new { id = result.Data!.Id }, request);
        }

        [HttpPut("milestones/{id}")]
        public async Task<IActionResult> UpdateMilestone(int id, [FromBody] UpdateMilestoneRequest request)
        {
            var updated = await _milestoneService.UpdateAsync(id, request);
            if (!updated)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }

        [HttpDelete("milestones/{id}")]
        public async Task<IActionResult> SoftDeleteMilestone(int id)
        {
            var deleted = await _milestoneService.SoftDeleteAsync(id);
            if (!deleted)
                throw new Exception("Ko cap nhat duoc");

            return NoContent();
        }

        [HttpPatch("milestones/reoder")]
        public async Task<IActionResult> ReoderMilestone([FromBody] List<int> ids)
        {
            var reordered = await _milestoneService.ReorderAsync(ids);
            if (!reordered)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }
    }
}