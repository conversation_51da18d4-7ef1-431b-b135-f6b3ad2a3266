﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Version;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VersionsController : ControllerBase
    {
        private readonly IVersionService _versionService;

        public VersionsController(IVersionService versionService)
        {
            _versionService = versionService;
        }

        [HttpGet("{projectKey}/versions")]
        public async Task<ActionResult<IEnumerable<Version>>> GetAllVersionsByProjectKey([FromRoute] string projectKey)
        {
            var versions = await _versionService.GetAllByProjectKeyAsync(projectKey);
            return Ok(versions);
        }

        [HttpGet("versions/{id}")]
        public async Task<ActionResult<Version>> GetVersionById(int id)
        {
            var version = await _versionService.GetByIdAsync(id);
            return Ok(version);
        }

        [HttpPost]
        public async Task<IActionResult> CreateVersion([FromBody] CreateVersionRequest request)
        {
            var result = await _versionService.CreateAsync(request);

            if (!result.IsSuccess)
                return result.ToActionResult();

            return CreatedAtAction(nameof(GetVersionById), new { id = result.Data!.Id }, request);
        }

        [HttpPut("versions/{id}")]
        public async Task<IActionResult> UpdateVersion(int id, [FromBody] UpdateVersionRequest request)
        {
            var updated = await _versionService.UpdateAsync(id, request);
            if (!updated)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }

        [HttpDelete("versions/{id}")]
        public async Task<IActionResult> SoftDeleteVersion(int id)
        {
            var deleted = await _versionService.SoftDeleteAsync(id);
            if (!deleted)
                throw new Exception("Ko cap nhat duoc");

            return NoContent();
        }

        [HttpPatch("versions/reoder")]
        public async Task<IActionResult> ReoderVersion([FromBody] List<int> ids)
        {
            var reordered = await _versionService.ReorderAsync(ids);
            if (!reordered)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }
    }
}