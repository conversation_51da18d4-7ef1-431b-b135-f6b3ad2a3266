using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Version;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.VersionRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class VersionService : IVersionService
    {
        private readonly IVersionRepository _versionRepository;
        private readonly IProjectAccessService _projectAccessService;
        private readonly ICurrentUserService _currentUserService;

        public VersionService(IVersionRepository versionRepository, IProjectAccessService projectAccessService, ICurrentUserService currentUserService)
        {
            _versionRepository = versionRepository;
            _projectAccessService = projectAccessService;
            _currentUserService = currentUserService;
        }

        public async Task<Result<VersionDto>> CreateAsync(CreateVersionRequest request)
        {
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<VersionDto>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Check if name is duplicated
            var isNameDuplicated = await _versionRepository.IsNameDuplicated(request.ProjectKey, request.Name);
            if (isNameDuplicated)
            {
                return Result<VersionDto>.Failure(ErrorCode.Conflict, "Tên phiên bản đã tồn tại trong dự án này.");
            }

            var version = new Version
            {
                Name = request.Name,
                Description = request.Description,
                Color = request.Color,
                ProjectKey = request.ProjectKey,
                Order = await _versionRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
                ProjectId = projectId.Value,
                CreatedById = _currentUserService.GetUserId(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                IsReleased = request.IsReleased
            };

            await _versionRepository.CreateAsync(version);

            return Result<VersionDto>.Success(version.Adapt<VersionDto>());
        }

        public async Task<Result<IEnumerable<VersionDto>>> GetAllByProjectKeyAsync(string projectKey)
        {
            IEnumerable<Version> versions = await _versionRepository.GetAllByProjectKeyAsync(projectKey);
            var versionDtos = versions.Adapt<IEnumerable<VersionDto>>();
            return Result<IEnumerable<VersionDto>>.Success(versionDtos);
        }

        public async Task<Result<VersionDto>> GetByIdAsync(int id)
        {
            var version = await _versionRepository.GetByIdAsync(id);
            if (version == null)
                return Result<VersionDto>.Failure(ErrorCode.NotFound, "Không tìm thấy phiên bản");

            return Result<VersionDto>.Success(version.Adapt<VersionDto>());
        }

        public async Task<Result<bool>> ReorderAsync(ReorderVersionRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.VersionIdsInOrder == null || !request.VersionIdsInOrder.Any())
            {
                return Result<bool>.Failure("Dữ liệu không hợp lệ.");
            }

            await _versionRepository.ReorderVersionsAsync(request.ProjectKey, request.VersionIdsInOrder);
            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> SoftDeleteAsync(int id)
        {
            var version = await _versionRepository.GetByIdAsync(id);
            if (version == null)
            {
                return Result<bool>.Failure("Không tìm thấy phiên bản.");
            }
            version.DeletedById = _currentUserService.GetUserId();
            version.DeletedAt = DateTime.UtcNow;
            await _versionRepository.SoftDeleteAsync(id);

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> UpdateAsync(int id, UpdateVersionRequest request)
        {
            var version = await _versionRepository.GetByIdAsync(id);
            if (version == null)
            {
                return Result<bool>.Failure("Không tìm thấy phiên bản.");
            }

            var existingVersions = await _versionRepository.GetAllByProjectKeyAsync(request.ProjectKey);
            var isNameDuplicated = existingVersions
                .Where(s => s.Id != id && s.ProjectKey == version.ProjectKey && s.Name == request.Name)
                .Any();

            if (isNameDuplicated)
            {
                return Result<bool>.Failure("Tên phiên bản đã tồn tại trong dự án này.");
            }
            version.Name = request.Name;
            version.Description = request.Description;
            version.Color = request.Color;
            version.LastModifiedAt = DateTime.UtcNow;
            version.LastModifiedById = _currentUserService.GetUserId();
            version.IsActive = request.IsActive;
            version.StartDate = request.StartDate;
            version.EndDate = request.EndDate;
            version.IsReleased = request.IsReleased;

            await _versionRepository.UpdateAsync(version);

            return Result<bool>.Success(true);
        }
    }
}
