﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.CategoryRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface ICategoryService
	{
		Task<Result<CategoryDto>> GetByIdAsync(int id);
		Task<Result<IEnumerable<CategoryDto>>> GetAllByProjectKeyAsync(string projectKey);
		Task<Result<CategoryDto>> CreateAsync(CreateCategoryRequest request);
		Task<Result<bool>> UpdateAsync(int id, UpdateCategoryRequest request);
		Task<Result<bool>> SoftDeleteAsync(int id);
		Task<Result<bool>> ReorderAsync(ReorderCategoryRequest request);
	}


}
