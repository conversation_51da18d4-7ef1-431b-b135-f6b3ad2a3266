using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class StatusRepository : Repository<Status>, IStatusRepository
    {
        public StatusRepository(ApplicationDbContext context) : base(context)
        {

        }

        public async Task<bool> IsNameDuplicated(string projectKey, string name)
        {
            return await _context.Statuses.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted);
        }

        public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
        {
            return await _context.Statuses
                .Where(c => c.ProjectKey == projectKey && !c.IsDeleted)
                .Select(c => (int?)c.Order)
                .MaxAsync() ?? 0;
        }

        public async Task<IEnumerable<Status>> GetAllByProjectKeyAsync(string projectKey)
        {
            var statuses = await _context.Statuses.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).OrderBy(c=>c.Order).ToListAsync();
            return statuses;
        }

        public async Task ReorderStatusesAsync(string projectKey, List<int> statusIdsInOrder)
        {
            var statuses = await _context.Statuses
                .Where(s => s.ProjectKey == projectKey && !s.IsDeleted && statusIdsInOrder.Contains(s.Id))
                .ToListAsync();

            for (int i = 0; i < statusIdsInOrder.Count; i++)
            {
                var status = statuses.FirstOrDefault(s => s.Id == statusIdsInOrder[i]);
                if (status != null)
                {
                    status.Order = i + 1;
                    _context.Statuses.Update(status);
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}
