﻿using CleanArchitecture.API.Requests;
using CleanArchitecture.API.Requests.IssueType;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.IssueTypeRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IIssueTypeService
    {
        Task<Result<IEnumerable<IssueTypeDto>>> GetAllByProjectKeyAsync(string projectKey);
        Task<Result<IssueTypeDto>> GetByIdAsync(int id);
        Task<Result<IssueTypeDto>> CreateAsync(CreateIssueTypeRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateIssueTypeRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderIssueTypeRequest request);
    }
}