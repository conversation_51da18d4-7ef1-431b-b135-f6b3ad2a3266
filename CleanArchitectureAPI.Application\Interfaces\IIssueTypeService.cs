﻿using CleanArchitecture.API.Requests;
using CleanArchitecture.API.Requests.IssueType;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IIssueTypeService
    {
        Task<IEnumerable<IssueType>> GetAllByProjectKeyAsync(string projectKey);
        Task<IssueType?> GetByIdAsync(int id);
        Task<Result<IssueType>> CreateAsync(CreateIssueTypeRequest request);
        Task<bool> UpdateAsync(int id, UpdateIssueTypeRequest request);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> ReorderAsync(List<int> ids);
    }
}