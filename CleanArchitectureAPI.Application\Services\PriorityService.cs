using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Priority;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.PriorityRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class PriorityService : IPriorityService
    {
        private readonly IPriorityRepository _priorityRepository;
        private readonly IProjectAccessService _projectAccessService;
        private readonly ICurrentUserService _currentUserService;

        public PriorityService(IPriorityRepository priorityRepository, IProjectAccessService projectAccessService, ICurrentUserService currentUserService)
        {
            _priorityRepository = priorityRepository;
            _projectAccessService = projectAccessService;
            _currentUserService = currentUserService;
        }

        public async Task<Result<PriorityDto>> CreateAsync(CreatePriorityRequest request)
        {
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<PriorityDto>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Check if name is duplicated
            var isNameDuplicated = await _priorityRepository.IsNameDuplicated(request.ProjectKey, request.Name);
            if (isNameDuplicated)
            {
                return Result<PriorityDto>.Failure(ErrorCode.Conflict, "Tên độ ưu tiên đã tồn tại trong dự án này.");
            }

            var priority = new Priority
            {
                Name = request.Name,
                Description = request.Description,
                Color = request.Color,
                ProjectKey = request.ProjectKey,
                Order = await _priorityRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
                ProjectId = projectId.Value,
                CreatedById = _currentUserService.GetUserId(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
            };

            await _priorityRepository.CreateAsync(priority);

            return Result<PriorityDto>.Success(priority.Adapt<PriorityDto>());
        }

        public async Task<Result<IEnumerable<PriorityDto>>> GetAllByProjectKeyAsync(string projectKey)
        {
            IEnumerable<Priority> priorities = await _priorityRepository.GetAllByProjectKeyAsync(projectKey);
            var priorityDtos = priorities.Adapt<IEnumerable<PriorityDto>>();
            return Result<IEnumerable<PriorityDto>>.Success(priorityDtos);
        }

        public async Task<Result<PriorityDto>> GetByIdAsync(int id)
        {
            var priority = await _priorityRepository.GetByIdAsync(id);
            if (priority == null)
                return Result<PriorityDto>.Failure(ErrorCode.NotFound, "Không tìm thấy độ ưu tiên");

            return Result<PriorityDto>.Success(priority.Adapt<PriorityDto>());
        }

        public async Task<Result<bool>> ReorderAsync(ReorderPriorityRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.PriorityIdsInOrder == null || !request.PriorityIdsInOrder.Any())
            {
                return Result<bool>.Failure("Dữ liệu không hợp lệ.");
            }

            await _priorityRepository.ReorderPrioritiesAsync(request.ProjectKey, request.PriorityIdsInOrder);
            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> SoftDeleteAsync(int id)
        {
            var priority = await _priorityRepository.GetByIdAsync(id);
            if (priority == null)
            {
                return Result<bool>.Failure("Không tìm thấy độ ưu tiên.");
            }
            priority.DeletedById = _currentUserService.GetUserId();
            priority.DeletedAt = DateTime.UtcNow;
            await _priorityRepository.SoftDeleteAsync(id);

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> UpdateAsync(int id, UpdatePriorityRequest request)
        {
            var priority = await _priorityRepository.GetByIdAsync(id);
            if (priority == null)
            {
                return Result<bool>.Failure("Không tìm thấy độ ưu tiên.");
            }

            var existingPriorities = await _priorityRepository.GetAllByProjectKeyAsync(request.ProjectKey);
            var isNameDuplicated = existingPriorities
                .Where(s => s.Id != id && s.ProjectKey == priority.ProjectKey && s.Name == request.Name)
                .Any();

            if (isNameDuplicated)
            {
                return Result<bool>.Failure("Tên độ ưu tiên đã tồn tại trong dự án này.");
            }
            priority.Name = request.Name;
            priority.Description = request.Description;
            priority.Color = request.Color;
            priority.LastModifiedAt = DateTime.UtcNow;
            priority.LastModifiedById = _currentUserService.GetUserId();
            priority.IsActive = request.IsActive;

            await _priorityRepository.UpdateAsync(priority);

            return Result<bool>.Success(true);
        }
    }
}
