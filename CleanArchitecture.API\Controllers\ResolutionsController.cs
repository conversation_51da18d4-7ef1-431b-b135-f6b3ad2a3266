﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Resolution;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ResolutionsController : ControllerBase
    {
        private readonly IResolutionService _resolutionService;

        public ResolutionsController(IResolutionService resolutionService)
        {
            _resolutionService = resolutionService;
        }

        [HttpGet("{projectKey}/resolutions")]
        public async Task<ActionResult<IEnumerable<Resolution>>> GetAllResolutionsByProjectKey([FromRoute] string projectKey)
        {
            var resolutions = await _resolutionService.GetAllByProjectKeyAsync(projectKey);
            return Ok(resolutions);
        }

        [HttpGet("resolutions/{id}")]
        public async Task<ActionResult<Resolution>> GetResolutionById(int id)
        {
            var resolution = await _resolutionService.GetByIdAsync(id);
            return Ok(resolution);
        }

        [HttpPost]
        public async Task<IActionResult> CreateResolution([FromBody] CreateResolutionRequest request)
        {
            var result = await _resolutionService.CreateAsync(request);

            if (!result.IsSuccess)
                return result.ToActionResult();

            return CreatedAtAction(nameof(GetResolutionById), new { id = result.Data!.Id }, request);
        }

        [HttpPut("resolutions/{id}")]
        public async Task<IActionResult> UpdateResolution(int id, [FromBody] UpdateResolutionRequest request)
        {
            var updated = await _resolutionService.UpdateAsync(id, request);
            if (!updated)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }

        [HttpDelete("resolutions/{id}")]
        public async Task<IActionResult> SoftDeleteResolution(int id)
        {
            var deleted = await _resolutionService.SoftDeleteAsync(id);
            if (!deleted)
                throw new Exception("Ko cap nhat duoc");

            return NoContent();
        }

        [HttpPatch("resolutions/reoder")]
        public async Task<IActionResult> ReoderResolution([FromBody] List<int> ids)
        {
            var reordered = await _resolutionService.ReorderAsync(ids);
            if (!reordered)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }
    }
}