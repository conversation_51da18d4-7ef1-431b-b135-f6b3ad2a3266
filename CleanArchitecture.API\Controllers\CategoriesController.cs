﻿
using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.CategoryRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
	[Authorize]
	[Route("api/[controller]")]
	[ApiController]
	public class CategoriesController : ControllerBase
	{
		private readonly ICategoryService _categoryService;

		public CategoriesController(ICategoryService categoryService)
		{
			_categoryService = categoryService;
		}

		[HttpGet("project/{projectKey}")]
		public async Task<IActionResult> GetAllCategoriesByProjectKey([FromRoute] string projectKey)
		{
			var result = await _categoryService.GetAllByProjectKeyAsync(projectKey);
			return result.ToActionResult();
		}

		[HttpGet("{id}")]
		public async Task<IActionResult> GetCategoryById(int id)
		{
			var result = await _categoryService.GetByIdAsync(id);
			return result.ToActionResult();
		}

		[HttpPost]
		public async Task<IActionResult> CreateCategory(CreateCategoryRequest request)
		{
			var result = await _categoryService.CreateAsync(request);

			if (!result.IsSuccess)
			{
				return result.ToActionResult();
			}
			return result.ToActionResult();
		}

		[HttpPut("{id}")]
		public async Task<IActionResult> UpdateCategory(int id, [FromBody] UpdateCategoryRequest request)
		{
			var result = await _categoryService.UpdateAsync(id, request);
			return result.ToActionResult();
		}

		[HttpDelete("{id}")]
		public async Task<IActionResult> SoftDeleteCategory(int id)
		{
			var result = await _categoryService.SoftDeleteAsync(id);
			return result.ToActionResult();
		}

		[HttpPatch("reorder")]
		public async Task<IActionResult> ReorderCategory([FromBody] ReorderCategoryRequest request)
		{
			var result = await _categoryService.ReorderAsync(request);
			return result.ToActionResult();
		}

	}
}
