﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests;
using CleanArchitecture.API.Requests.IssueType;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.IssueTypeRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class IssueTypesController : ControllerBase
    {
        private readonly IIssueTypeService _issueTypeService;

        public IssueTypesController(IIssueTypeService issueTypeService)
        {
            _issueTypeService = issueTypeService;
        }

        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllIssueTypesByProjectKey([FromRoute] string projectKey)
        {
            var result = await _issueTypeService.GetAllByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetIssueTypeById(int id)
        {
            var result = await _issueTypeService.GetByIdAsync(id);
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreateIssueType([FromBody] CreateIssueTypeRequest request)
        {
            var result = await _issueTypeService.CreateAsync(request);

            if (!result.IsSuccess)
            {
                return result.ToActionResult();
            }
            return result.ToActionResult();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateIssueType(int id, [FromBody] UpdateIssueTypeRequest request)
        {
            var result = await _issueTypeService.UpdateAsync(id, request);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> SoftDeleteIssueType(int id)
        {
            var result = await _issueTypeService.SoftDeleteAsync(id);
            return result.ToActionResult();
        }

        [HttpPatch("reorder")]
        public async Task<IActionResult> ReorderIssueType([FromBody] ReorderIssueTypeRequest request)
        {
            var result = await _issueTypeService.ReorderAsync(request);
            return result.ToActionResult();
        }
    }
}