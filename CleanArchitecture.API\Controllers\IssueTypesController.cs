﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests;
using CleanArchitecture.API.Requests.IssueType;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class IssueTypesController : ControllerBase
    {
        private readonly IIssueTypeService _issueTypeService;

        public IssueTypesController(IIssueTypeService issueTypeService)
        {
            _issueTypeService = issueTypeService;
        }

        [HttpGet("{projectKey}/issue-types")]
        public async Task<ActionResult<IEnumerable<IssueType>>> GetAllIssueTypesByProjectKey([FromRoute] string projectKey)
        {
            var issueTypes = await _issueTypeService.GetAllByProjectKeyAsync(projectKey);
            return Ok(issueTypes);
        }

        [HttpGet("issue-types/{id}")]
        public async Task<ActionResult<IssueType>> GetIssueTypeById(int id)
        {
            var issueType = await _issueTypeService.GetByIdAsync(id);
            return Ok(issueType);
        }

        [HttpPost]
        public async Task<IActionResult> CreateIssueType([FromBody] CreateIssueTypeRequest request)
        {
            var result = await _issueTypeService.CreateAsync(request);

            if (!result.IsSuccess)
                return result.ToActionResult();

            return CreatedAtAction(nameof(GetIssueTypeById), new { id = result.Data!.Id }, request);
        }

        [HttpPut("issue-types/{id}")]
        public async Task<IActionResult> UpdateIssueType(int id, [FromBody] UpdateIssueTypeRequest request)
        {
            var updated = await _issueTypeService.UpdateAsync(id, request);
            if (!updated)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }

        [HttpDelete("issue-types/{id}")]
        public async Task<IActionResult> SoftDeleteIssueType(int id)
        {
            var deleted = await _issueTypeService.SoftDeleteAsync(id);
            if (!deleted)
                throw new Exception("Ko cap nhat duoc");

            return NoContent();
        }

        [HttpPatch("issue-types/reoder")]
        public async Task<IActionResult> ReoderIssueType([FromBody] List<int> ids)
        {
            var reordered = await _issueTypeService.ReorderAsync(ids);
            if (!reordered)
                throw new Exception("Ko cap nhat duoc");

            return Ok();
        }
    }
}